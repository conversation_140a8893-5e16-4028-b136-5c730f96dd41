'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, Briefcase, CheckCircle, Circle, Bookmark, BookmarkCheck, BookOpen, Star, ExternalLink, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface LearningResource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string;
  duration?: string;
  cost: string;
  averageRating: number;
  totalRatings: number;
}

interface CareerPath {
  id: string;
  name: string;
  slug: string;
  overview: string;
  pros: string[];
  cons: string[];
  actionableSteps: string[];
  isActive: boolean;
  learningResources?: LearningResource[];
  relatedSkills?: { id: string; name: string; }[];
}

export default function CareerPathDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [careerPath, setCareerPath] = useState<CareerPath | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [, setUserProgress] = useState<Record<string, unknown>>({});

  const fetchCareerPath = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/career-paths');

      if (!response.ok) {
        throw new Error('Failed to fetch career paths');
      }

      const data = await response.json();
      const resolvedParams = await params;
      const foundPath = data.data?.careerPaths?.find((path: CareerPath) => path.id === resolvedParams.id);

      if (!foundPath) {
        setError('Career path not found');
        return;
      }

      setCareerPath(foundPath);
    } catch (err) {
      console.error('Error fetching career path:', err);
      setError('Failed to load career path');
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    fetchCareerPath();
  }, [fetchCareerPath]);





  const handleBookmark = async () => {
    if (!session?.user?.id) {
      router.push('/login');
      return;
    }

    try {
      const resolvedParams = await params;
      const response = await fetch('/api/career-paths', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'bookmark',
          pathId: resolvedParams.id,
          isBookmarked: !isBookmarked,
        }),
      });

      if (response.ok) {
        setIsBookmarked(!isBookmarked);
      }
    } catch (err) {
      console.error('Error bookmarking career path:', err);
    }
  };

  const handleStepToggle = async (stepIndex: number) => {
    if (!session?.user?.id) {
      router.push('/login');
      return;
    }

    const isCompleted = completedSteps.includes(stepIndex);
    const newCompletedSteps = isCompleted
      ? completedSteps.filter(step => step !== stepIndex)
      : [...completedSteps, stepIndex];

    setCompletedSteps(newCompletedSteps);

    try {
      const resolvedParams = await params;
      await fetch('/api/career-paths', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'toggleStep',
          pathId: resolvedParams.id,
          itemId: stepIndex,
          completed: !isCompleted,
        }),
      });
    } catch (err) {
      console.error('Error updating step completion:', err);
    }
  };

  const handleResourceProgress = async (resourceId: string, status: string) => {
    if (!session?.user?.id) return;

    try {
      const response = await fetch('/api/learning-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resourceId,
          status
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update resource progress');
      }

      const result = await response.json();
      if (result.success) {
        setUserProgress(prev => ({
          ...prev,
          [resourceId]: result.data
        }));
      }
    } catch (error) {
      console.error('Error updating resource progress:', error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading career path...</div>
      </div>
    );
  }

  if (error || !careerPath) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400">{error || 'Career path not found'}</p>
          <Link href="/career-paths" className="text-blue-600 hover:underline mt-4 inline-block">
            Back to Career Paths
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-6">
        <Link
          href="/career-paths"
          className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Career Paths
        </Link>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div className="flex items-start justify-between mb-6">
          <div className="flex items-center gap-4">
            <Briefcase className="h-12 w-12 text-blue-600 dark:text-blue-400" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                {careerPath.name}
              </h1>
            </div>
          </div>
          {status === 'authenticated' && (
            <Button
              onClick={handleBookmark}
              variant={isBookmarked ? "default" : "outline"}
              className="flex items-center gap-2"
            >
              {isBookmarked ? (
                <>
                  <BookmarkCheck className="h-4 w-4" />
                  Bookmarked
                </>
              ) : (
                <>
                  <Bookmark className="h-4 w-4" />
                  Bookmark
                </>
              )}
            </Button>
          )}
        </div>

        <div className="prose dark:prose-invert max-w-none mb-8">
          <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
            {careerPath.overview}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-green-800 dark:text-green-200 mb-4 flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Advantages
            </h2>
            <ul className="space-y-3">
              {careerPath.pros.map((pro, index) => (
                <li key={index} className="text-green-700 dark:text-green-300 flex items-start gap-2">
                  <Circle className="h-3 w-3 mt-1.5 text-green-500 fill-current flex-shrink-0" />
                  {pro}
                </li>
              ))}
            </ul>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-4 flex items-center gap-2">
              <Circle className="h-5 w-5" />
              Challenges
            </h2>
            <ul className="space-y-3">
              {careerPath.cons.map((con, index) => (
                <li key={index} className="text-red-700 dark:text-red-300 flex items-start gap-2">
                  <Circle className="h-3 w-3 mt-1.5 text-red-500 fill-current flex-shrink-0" />
                  {con}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
          <h2 className="text-xl font-semibold text-blue-800 dark:text-blue-200 mb-6">
            Action Plan: Getting Started
          </h2>
          <div className="space-y-4">
            {careerPath.actionableSteps.map((step, index) => (
              <div
                key={index}
                className="flex items-start gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm"
              >
                {status === 'authenticated' ? (
                  <button
                    onClick={() => handleStepToggle(index)}
                    className="mt-1 flex-shrink-0"
                  >
                    {completedSteps.includes(index) ? (
                      <CheckCircle className="h-6 w-6 text-green-500" />
                    ) : (
                      <Circle className="h-6 w-6 text-gray-400 hover:text-blue-500" />
                    )}
                  </button>
                ) : (
                  <Circle className="h-6 w-6 text-gray-400 mt-1 flex-shrink-0" />
                )}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </span>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Step {index + 1}
                    </h3>
                  </div>
                  <p className={`text-gray-700 dark:text-gray-300 ${
                    completedSteps.includes(index) ? 'line-through opacity-75' : ''
                  }`}>
                    {step}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {status === 'unauthenticated' && (
            <div className="mt-6 text-center">
              <p className="text-blue-700 dark:text-blue-300 mb-4">
                Sign up to track your progress through these action steps!
              </p>
              <Button asChild>
                <Link href="/signup">Get Started</Link>
              </Button>
            </div>
          )}
        </div>

        {/* Learning Resources Section */}
        {careerPath.learningResources && careerPath.learningResources.length > 0 && (
          <div className="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-purple-800 dark:text-purple-200 mb-6 flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Recommended Learning Resources
            </h2>
            <p className="text-purple-700 dark:text-purple-300 mb-6">
              Curated resources to help you build the skills needed for this career path.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {careerPath.learningResources.map((resource) => (
                <div
                  key={resource.id}
                  className="bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                        {resource.title}
                      </h3>
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          resource.skillLevel === 'BEGINNER' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          resource.skillLevel === 'INTERMEDIATE' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {resource.skillLevel.toLowerCase()}
                        </span>
                        {resource.cost === 'FREE' && (
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-xs font-medium">
                            Free
                          </span>
                        )}
                        {resource.averageRating > 0 && (
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3 text-yellow-500 fill-current" />
                            <span className="text-xs text-gray-600 dark:text-gray-400">
                              {resource.averageRating.toFixed(1)} ({resource.totalRatings})
                            </span>
                          </div>
                        )}
                      </div>
                      {resource.author && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                          by {resource.author}
                          {resource.duration && ` • ${resource.duration}`}
                        </p>
                      )}
                    </div>
                  </div>

                  <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 leading-relaxed">
                    {resource.description}
                  </p>

                  <div className="flex gap-2">
                    <Button
                      asChild
                      size="sm"
                      className="flex-1"
                    >
                      <a
                        href={resource.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center gap-2"
                      >
                        <Play className="h-4 w-4" />
                        Start Learning
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </Button>

                    {status === 'authenticated' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResourceProgress(resource.id, 'BOOKMARKED')}
                        className="px-3"
                      >
                        <Bookmark className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 text-center">
              <Button asChild variant="outline">
                <Link href="/resources?category=all">
                  View All Learning Resources
                </Link>
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}