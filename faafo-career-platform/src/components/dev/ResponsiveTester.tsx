'use client';

import * as React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useResponsiveDesign, useTouchDevice } from '@/hooks/useResponsiveDesign';
import { Monitor, Smartphone, Tablet, Laptop, Tv, RotateCcw } from 'lucide-react';

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ReactNode;
  category: 'mobile' | 'tablet' | 'desktop';
}

const devicePresets: DevicePreset[] = [
  // Mobile devices
  { name: 'iPhone SE', width: 375, height: 667, icon: <Smartphone className="h-4 w-4" />, category: 'mobile' },
  { name: 'iPhone 12/13/14', width: 390, height: 844, icon: <Smartphone className="h-4 w-4" />, category: 'mobile' },
  { name: 'iPhone 12/13/14 Pro Max', width: 428, height: 926, icon: <Smartphone className="h-4 w-4" />, category: 'mobile' },
  { name: 'Samsung Galaxy S21', width: 384, height: 854, icon: <Smartphone className="h-4 w-4" />, category: 'mobile' },
  { name: 'Google Pixel 5', width: 393, height: 851, icon: <Smartphone className="h-4 w-4" />, category: 'mobile' },
  
  // Tablets
  { name: 'iPad Mini', width: 768, height: 1024, icon: <Tablet className="h-4 w-4" />, category: 'tablet' },
  { name: 'iPad Air', width: 820, height: 1180, icon: <Tablet className="h-4 w-4" />, category: 'tablet' },
  { name: 'iPad Pro 11"', width: 834, height: 1194, icon: <Tablet className="h-4 w-4" />, category: 'tablet' },
  { name: 'iPad Pro 12.9"', width: 1024, height: 1366, icon: <Tablet className="h-4 w-4" />, category: 'tablet' },
  { name: 'Surface Pro', width: 912, height: 1368, icon: <Tablet className="h-4 w-4" />, category: 'tablet' },
  
  // Desktop
  { name: 'MacBook Air', width: 1280, height: 832, icon: <Laptop className="h-4 w-4" />, category: 'desktop' },
  { name: 'MacBook Pro 14"', width: 1512, height: 982, icon: <Laptop className="h-4 w-4" />, category: 'desktop' },
  { name: 'MacBook Pro 16"', width: 1728, height: 1117, icon: <Laptop className="h-4 w-4" />, category: 'desktop' },
  { name: '1080p Monitor', width: 1920, height: 1080, icon: <Monitor className="h-4 w-4" />, category: 'desktop' },
  { name: '1440p Monitor', width: 2560, height: 1440, icon: <Monitor className="h-4 w-4" />, category: 'desktop' },
  { name: '4K Monitor', width: 3840, height: 2160, icon: <Tv className="h-4 w-4" />, category: 'desktop' },
];

/**
 * Development tool for testing responsive design
 * Only renders in development mode
 */
export function ResponsiveTester() {
  const { windowSize, currentBreakpoint, isMobile, isTablet, isDesktop } = useResponsiveDesign();
  const { isTouchDevice, hasHover } = useTouchDevice();
  const [selectedDevice, setSelectedDevice] = React.useState<string>('');
  const [isLandscape, setIsLandscape] = React.useState(false);

  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const currentDevice = devicePresets.find(d => d.name === selectedDevice);

  const simulateDevice = React.useCallback((device: DevicePreset) => {
    const width = isLandscape ? device.height : device.width;
    const height = isLandscape ? device.width : device.height;
    
    // This is a simulation - in a real implementation, you might use iframe or other methods
    // For now, we'll just show the information
    console.log(`Simulating ${device.name}: ${width}x${height}`);
  }, [isLandscape]);

  const resetToActual = React.useCallback(() => {
    setSelectedDevice('');
    setIsLandscape(false);
  }, []);

  const toggleOrientation = React.useCallback(() => {
    setIsLandscape(!isLandscape);
  }, [isLandscape]);

  const getBreakpointColor = (bp: string) => {
    switch (bp) {
      case 'xs': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'sm': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'md': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'lg': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'xl': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case '2xl': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCategoryDevices = (category: 'mobile' | 'tablet' | 'desktop') => {
    return devicePresets.filter(d => d.category === category);
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <Card className="w-80 shadow-lg border-2">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Responsive Tester</CardTitle>
          <CardDescription>
            Test responsive design across devices
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Current Status */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Current Size:</span>
              <span className="text-sm font-mono">
                {windowSize.width} × {windowSize.height}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Breakpoint:</span>
              <Badge className={getBreakpointColor(currentBreakpoint)}>
                {currentBreakpoint}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Device Type:</span>
              <div className="flex gap-1">
                {isMobile && <Badge variant="outline">Mobile</Badge>}
                {isTablet && <Badge variant="outline">Tablet</Badge>}
                {isDesktop && <Badge variant="outline">Desktop</Badge>}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Capabilities:</span>
              <div className="flex gap-1">
                {isTouchDevice && <Badge variant="secondary">Touch</Badge>}
                {hasHover && <Badge variant="secondary">Hover</Badge>}
              </div>
            </div>
          </div>

          {/* Device Simulation */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Simulate Device:</span>
              {selectedDevice && (
                <Button
                  onClick={toggleOrientation}
                  variant="outline"
                  size="sm"
                >
                  <RotateCcw className="h-3 w-3" />
                </Button>
              )}
            </div>

            <Select value={selectedDevice} onValueChange={setSelectedDevice}>
              <SelectTrigger>
                <SelectValue placeholder="Select a device..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Actual Size</SelectItem>
                
                {/* Mobile Devices */}
                <div className="px-2 py-1 text-xs font-semibold text-muted-foreground">
                  Mobile Devices
                </div>
                {getCategoryDevices('mobile').map(device => (
                  <SelectItem key={device.name} value={device.name}>
                    <div className="flex items-center gap-2">
                      {device.icon}
                      <span>{device.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {device.width}×{device.height}
                      </span>
                    </div>
                  </SelectItem>
                ))}

                {/* Tablets */}
                <div className="px-2 py-1 text-xs font-semibold text-muted-foreground">
                  Tablets
                </div>
                {getCategoryDevices('tablet').map(device => (
                  <SelectItem key={device.name} value={device.name}>
                    <div className="flex items-center gap-2">
                      {device.icon}
                      <span>{device.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {device.width}×{device.height}
                      </span>
                    </div>
                  </SelectItem>
                ))}

                {/* Desktop */}
                <div className="px-2 py-1 text-xs font-semibold text-muted-foreground">
                  Desktop
                </div>
                {getCategoryDevices('desktop').map(device => (
                  <SelectItem key={device.name} value={device.name}>
                    <div className="flex items-center gap-2">
                      {device.icon}
                      <span>{device.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {device.width}×{device.height}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {currentDevice && (
              <div className="p-2 bg-muted rounded text-sm">
                <div className="flex items-center gap-2 mb-1">
                  {currentDevice.icon}
                  <span className="font-medium">{currentDevice.name}</span>
                  {isLandscape && <Badge variant="outline">Landscape</Badge>}
                </div>
                <div className="text-xs text-muted-foreground">
                  Simulated: {isLandscape ? currentDevice.height : currentDevice.width} × {isLandscape ? currentDevice.width : currentDevice.height}
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              onClick={resetToActual}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              Reset
            </Button>
            {currentDevice && (
              <Button
                onClick={() => simulateDevice(currentDevice)}
                size="sm"
                className="flex-1"
              >
                Apply
              </Button>
            )}
          </div>

          {/* Quick Info */}
          <div className="text-xs text-muted-foreground space-y-1">
            <div>• Use browser dev tools for actual device simulation</div>
            <div>• This tool shows current responsive state</div>
            <div>• Test touch interactions on actual devices</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
